
import { motion } from "framer-motion";
import Navigation from "@/components/portfolio/Navigation";
import ContactSection from "@/components/portfolio/ContactSection";
import { useState, useEffect } from "react";

const Contact = () => {
  const [darkMode, setDarkMode] = useState(true); // Default to dark mode

  useEffect(() => {
    // Set dark mode as default on initial load
    document.documentElement.classList.add('dark');

    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <Navigation darkMode={darkMode} setDarkMode={setDarkMode} />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-br from-background via-background to-secondary/5 text-foreground pt-16"
      >
        <ContactSection />
      </motion.div>
    </div>
  );
};

export default Contact;
