
import { motion } from "framer-motion";
import Navigation from "@/components/portfolio/Navigation";
import BlogSection from "@/components/portfolio/BlogSection";
import { useState, useEffect } from "react";

const Blog = () => {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <Navigation darkMode={darkMode} setDarkMode={setDarkMode} />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-br from-background via-background to-secondary/5 text-foreground pt-16"
      >
        <BlogSection />
      </motion.div>
    </div>
  );
};

export default Blog;
