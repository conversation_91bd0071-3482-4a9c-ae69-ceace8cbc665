
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Github, Linkedin, Mail, Send, MapPin, Phone, Zap, Cpu, Globe } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

const ContactSection = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Message Sent!",
      description: "Thank you for reaching out. I'll get back to you soon.",
    });
    setFormData({ name: "", email: "", subject: "", message: "" });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const socialLinks = [
    {
      name: "LinkedIn",
      icon: Linkedin,
      url: "https://www.linkedin.com/in/bala-mugesh-m-k/",
      color: "neon-blue",
      bgColor: "from-neon-blue/20 to-neon-blue/10"
    },
    {
      name: "GitHub",
      icon: Github,
      url: "https://github.com/BMugesh",
      color: "neon-purple",
      bgColor: "from-neon-purple/20 to-neon-purple/10"
    },
    {
      name: "Email",
      icon: Mail,
      url: "mailto:<EMAIL>",
      color: "neon-green",
      bgColor: "from-neon-green/20 to-neon-green/10"
    }
  ];

  const contactInfo = [
    {
      icon: MapPin,
      label: "Location",
      value: "1/8c, Manakaavalam Pillai Hospital Rd, Palayamkottai, Tirunelveli, Tamil Nadu 627002"
    },
    {
      icon: Phone,
      label: "Phone",
      value: "+918778848565"
    },
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>"
    }
  ];

  return (
    <section id="contact" className="py-20 relative">
      {/* Tech Background */}
      <div className="absolute inset-0 tech-grid opacity-10"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-orbitron font-bold mb-6 neon-cyan animate-glow-pulse">
            Let's Connect
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto font-rajdhani leading-relaxed">
            Have a <span className="neon-green">project</span> in mind or want to discuss opportunities?
            I'd love to hear from you. Let's build something <span className="neon-purple">amazing</span> together.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Card className="glass border-neon-blue/20 hover:border-neon-cyan/40 transition-all duration-300 relative overflow-hidden">
              {/* Holographic effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/5 via-transparent to-neon-purple/5 opacity-50"></div>

              <CardHeader className="relative z-10">
                <CardTitle className="font-orbitron text-neon-cyan flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  Send a Message
                </CardTitle>
                <CardDescription className="text-gray-300 font-rajdhani">
                  Fill out the form below and I'll get back to you as soon as possible.
                </CardDescription>
              </CardHeader>
              <CardContent className="relative z-10">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="font-rajdhani font-semibold text-neon-blue">Name</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Your name"
                        className="glass border-neon-blue/30 focus:border-neon-cyan/50 bg-transparent text-white placeholder:text-gray-400 font-rajdhani"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="font-rajdhani font-semibold text-neon-green">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="<EMAIL>"
                        className="glass border-neon-green/30 focus:border-neon-green/50 bg-transparent text-white placeholder:text-gray-400 font-rajdhani"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="font-rajdhani font-semibold text-neon-purple">Subject</Label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      placeholder="Project discussion, collaboration, etc."
                      className="glass border-neon-purple/30 focus:border-neon-purple/50 bg-transparent text-white placeholder:text-gray-400 font-rajdhani"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="message" className="font-rajdhani font-semibold text-neon-cyan">Message</Label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      placeholder="Tell me about your project or how we can work together..."
                      className="glass border-neon-cyan/30 focus:border-neon-cyan/50 bg-transparent text-white placeholder:text-gray-400 font-rajdhani flex min-h-[120px] w-full rounded-md px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neon-cyan/50 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none"
                    />
                  </div>
                  <Button type="submit" className="w-full tech-button group font-orbitron font-semibold">
                    <Send className="mr-2 h-4 w-4" />
                    Send Message
                    <Zap className="ml-2 h-4 w-4 group-hover:rotate-12 transition-transform" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Info & Social */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Contact Information */}
            <Card className="glass border-neon-green/20 hover:border-neon-green/40 transition-all duration-300">
              <CardHeader>
                <CardTitle className="font-orbitron text-neon-green flex items-center">
                  <Cpu className="h-5 w-5 mr-2" />
                  Contact Information
                </CardTitle>
                <CardDescription className="text-gray-300 font-rajdhani">
                  Reach out through any of these channels
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={info.label}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-3 p-3 glass rounded-lg hover:bg-white/5 transition-all duration-300"
                  >
                    <div className="p-2 glass border border-neon-green/30 rounded-full">
                      <info.icon className="h-4 w-4 text-neon-green" />
                    </div>
                    <div>
                      <div className="font-medium font-rajdhani text-neon-green">{info.label}</div>
                      <div className="text-gray-300 font-roboto-mono text-sm">{info.value}</div>
                    </div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>

            {/* Social Links */}
            <Card className="glass border-neon-purple/20 hover:border-neon-purple/40 transition-all duration-300">
              <CardHeader>
                <CardTitle className="font-orbitron text-neon-purple flex items-center">
                  <Globe className="h-5 w-5 mr-2" />
                  Connect With Me
                </CardTitle>
                <CardDescription className="text-gray-300 font-rajdhani">
                  Follow my journey and connect on social platforms
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex space-x-6 justify-center">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ scale: 1.1, y: -5 }}
                      className={`flex items-center justify-center w-16 h-16 rounded-full glass border border-${social.color}/30 hover:border-${social.color}/60 bg-gradient-to-br ${social.bgColor} transition-all duration-300 group relative overflow-hidden`}
                    >
                      {/* Glow effect */}
                      <div className={`absolute inset-0 bg-${social.color}/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-sm`}></div>
                      <social.icon className={`h-6 w-6 text-${social.color} relative z-10`} />
                    </motion.a>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Call to Action */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <Card className="holographic border-neon-cyan/30 relative overflow-hidden">
                {/* Animated background */}
                <div className="absolute inset-0 bg-gradient-to-br from-neon-cyan/10 via-neon-blue/5 to-neon-purple/10"></div>

                <CardContent className="text-center pt-6 relative z-10">
                  <h3 className="text-xl font-orbitron font-semibold mb-2 neon-cyan">
                    Ready to Start Your Project?
                  </h3>
                  <p className="text-gray-300 mb-4 font-rajdhani">
                    Let's discuss how <span className="neon-purple font-semibold">ORIVOX</span> can bring your vision to life
                  </p>
                  <Button size="lg" className="tech-button group font-orbitron font-semibold">
                    <Zap className="mr-2 h-4 w-4" />
                    Schedule a Call
                    <Send className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>

        {/* Floating Tech Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[Send, Mail, Zap, Cpu, Globe].map((Icon, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -25, 0],
                rotate: [0, 180, 360],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 10 + Math.random() * 5,
                repeat: Infinity,
                delay: Math.random() * 6,
              }}
            >
              <Icon className="h-6 w-6 text-neon-cyan/20" />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
