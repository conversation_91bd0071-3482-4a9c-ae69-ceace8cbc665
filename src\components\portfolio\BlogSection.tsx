
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, Clock, ArrowRight } from "lucide-react";
import { useState } from "react";

const BlogSection = () => {
  const [filter, setFilter] = useState("all");

  const blogPosts = [
    {
      title: "The Future of AI Agent Development",
      description: "Exploring the latest trends and techniques in building intelligent AI agents that can perform complex tasks autonomously.",
      category: "AI",
      date: "2024-12-01",
      readTime: "8 min read",
      tags: ["AI", "Agents", "Development"]
    },
    {
      title: "Building ORIVOX: From Idea to Startup",
      description: "The journey of creating a tech startup focused on immersive digital experiences and the lessons learned along the way.",
      category: "Startup",
      date: "2024-11-28",
      readTime: "12 min read",
      tags: ["Startup", "Entrepreneurship", "Tech"]
    },
    {
      title: "Mastering Prompt Engineering for GPT Models",
      description: "Advanced techniques and best practices for crafting effective prompts that get the most out of large language models.",
      category: "AI",
      date: "2024-11-25",
      readTime: "10 min read",
      tags: ["Prompt Engineering", "GPT", "AI"]
    },
    {
      title: "3D Web Development with Three.js and React",
      description: "Creating immersive 3D experiences on the web using modern frameworks and WebGL technologies.",
      category: "Projects",
      date: "2024-11-20",
      readTime: "15 min read",
      tags: ["3D", "React", "WebGL", "Three.js"]
    },
    {
      title: "The Rise of Immersive Learning Platforms",
      description: "How VR, AR, and AI are revolutionizing education and creating new opportunities for interactive learning.",
      category: "AI",
      date: "2024-11-15",
      readTime: "7 min read",
      tags: ["Education", "VR", "AR", "Learning"]
    },
    {
      title: "Scaling a Tech Startup: Lessons from ORIVOX",
      description: "Strategic insights and practical advice for growing a technology startup in today's competitive landscape.",
      category: "Startup",
      date: "2024-11-10",
      readTime: "9 min read",
      tags: ["Scaling", "Strategy", "Business"]
    }
  ];

  const categories = [
    { key: "all", label: "All Posts" },
    { key: "AI", label: "AI & Tech" },
    { key: "Startup", label: "Startup" },
    { key: "Projects", label: "Projects" }
  ];

  const filteredPosts = filter === "all" 
    ? blogPosts 
    : blogPosts.filter(post => post.category === filter);

  return (
    <section id="blog" className="py-20 bg-secondary/5">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">Latest Articles</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Insights on AI, startup development, and cutting-edge technology. 
            Stay updated with my latest thoughts and discoveries.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <Button
              key={category.key}
              variant={filter === category.key ? "default" : "outline"}
              onClick={() => setFilter(category.key)}
              className="transition-all duration-300"
            >
              {category.label}
            </Button>
          ))}
        </motion.div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPosts.map((post, index) => (
            <motion.div
              key={post.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 cursor-pointer">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary">{post.category}</Badge>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  <CardTitle className="text-lg group-hover:text-primary transition-colors line-clamp-2">
                    {post.title}
                  </CardTitle>
                  <CardDescription className="line-clamp-3">
                    {post.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 mr-2" />
                      {new Date(post.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <Button variant="ghost" className="w-full group/btn">
                      Read Article
                      <ArrowRight className="ml-2 h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Button size="lg" variant="outline">
            View All Articles
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default BlogSection;
