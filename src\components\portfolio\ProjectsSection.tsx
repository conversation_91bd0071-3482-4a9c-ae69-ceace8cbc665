
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, Code, Sparkles, Cpu, Zap, Globe } from "lucide-react";
import { useState } from "react";

const ProjectsSection = () => {
  const [filter, setFilter] = useState("all");

  const orivoxProjects = [
    {
      name: "Script&Style",
      description: "Freelancing platform with immersive 3D interactions and seamless project management",
      stack: ["React", "Three.js", "Node.js", "MongoDB"],
      tags: ["3D", "Web Development", "Freelancing"],
      category: "platform"
    },
    {
      name: "TD - Transparent Display",
      description: "Cutting-edge transparent display technology for next-generation interfaces",
      stack: ["Python", "OpenCV", "Hardware Integration"],
      tags: ["Hardware", "AI", "Innovation"],
      category: "hardware"
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      description: "Immersive learning platform leveraging AI and VR for enhanced education",
      stack: ["React", "AI/ML", "WebXR", "PostgreSQL"],
      tags: ["AI", "Education", "VR"],
      category: "ai"
    },
    {
      name: "ExpoEXO",
      description: "Advanced expo and project showcase platform with interactive presentations",
      stack: ["Next.js", "Three.js", "WebGL", "Prisma"],
      tags: ["3D", "Showcase", "Interactive"],
      category: "platform"
    }
  ];

  const filterCategories = [
    { key: "all", label: "All Projects" },
    { key: "platform", label: "Platforms" },
    { key: "ai", label: "AI Projects" },
    { key: "hardware", label: "Hardware" }
  ];

  const filteredProjects = filter === "all" 
    ? orivoxProjects 
    : orivoxProjects.filter(project => project.category === filter);

  return (
    <section id="projects" className="py-20 relative">
      {/* Tech Background */}
      <div className="absolute inset-0 tech-grid opacity-10"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-orbitron font-bold mb-6 neon-cyan animate-glow-pulse">
            My Startup: ORIVOX
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto font-rajdhani leading-relaxed">
            Building the <span className="neon-green">future</span> through innovative technology solutions.
            Explore our flagship products and <span className="neon-purple">cutting-edge projects</span>.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {filterCategories.map((category) => (
            <Button
              key={category.key}
              variant={filter === category.key ? "default" : "outline"}
              onClick={() => setFilter(category.key)}
              className={`font-rajdhani font-semibold transition-all duration-300 ${
                filter === category.key
                  ? 'tech-button'
                  : 'glass border-neon-blue/30 text-gray-300 hover:border-neon-cyan/50 hover:text-neon-cyan'
              }`}
            >
              {category.label}
            </Button>
          ))}
        </motion.div>

        {/* Featured ORIVOX Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <Card className="holographic border-neon-purple/30 relative overflow-hidden">
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/5 via-neon-purple/5 to-neon-cyan/5"></div>

            <CardHeader className="text-center relative z-10">
              <div className="flex justify-center mb-4">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="p-3 glass border border-neon-purple/30 rounded-full"
                >
                  <Sparkles className="h-12 w-12 neon-purple" />
                </motion.div>
              </div>
              <CardTitle className="text-3xl mb-4 font-orbitron neon-cyan">
                ORIVOX Ecosystem
              </CardTitle>
              <CardDescription className="text-lg text-gray-300 font-rajdhani">
                A comprehensive suite of <span className="neon-green">innovative products</span> designed to transform
                how we interact with technology and learn in the <span className="neon-blue">digital age</span>.
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="h-full glass border-neon-blue/20 hover:border-neon-cyan/40 hover:shadow-lg hover:shadow-neon-cyan/10 transition-all duration-300 relative overflow-hidden group">
                {/* Holographic hover effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/5 via-transparent to-neon-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <CardHeader className="relative z-10">
                  <div className="flex justify-between items-start mb-2">
                    <CardTitle className="text-xl font-orbitron group-hover:text-neon-cyan transition-colors">
                      {project.name}
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button size="sm" variant="ghost" className="p-2 glass border border-neon-blue/30 hover:border-neon-cyan/50 transition-all duration-300">
                        <Code className="h-4 w-4 text-neon-blue" />
                      </Button>
                      <Button size="sm" variant="ghost" className="p-2 glass border border-neon-green/30 hover:border-neon-green/50 transition-all duration-300">
                        <ExternalLink className="h-4 w-4 text-neon-green" />
                      </Button>
                    </div>
                  </div>
                  <CardDescription className="text-base text-gray-300 font-rajdhani leading-relaxed">
                    {project.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="relative z-10">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2 font-rajdhani text-neon-blue flex items-center">
                        <Cpu className="h-4 w-4 mr-2" />
                        Tech Stack:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {project.stack.map((tech) => (
                          <Badge
                            key={tech}
                            className="glass border-neon-green/30 text-neon-green hover:border-neon-green/50 transition-all duration-300 font-roboto-mono"
                          >
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2 font-rajdhani text-neon-purple flex items-center">
                        <Zap className="h-4 w-4 mr-2" />
                        Tags:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {project.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="outline"
                            className="border-neon-purple/30 text-neon-purple hover:border-neon-purple/50 transition-all duration-300 font-roboto-mono"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Floating Tech Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[Code, Cpu, Globe, Zap].map((Icon, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                rotate: [0, 180, 360],
                opacity: [0.1, 0.4, 0.1],
              }}
              transition={{
                duration: 12 + Math.random() * 6,
                repeat: Infinity,
                delay: Math.random() * 8,
              }}
            >
              <Icon className="h-8 w-8 text-neon-blue/20" />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
