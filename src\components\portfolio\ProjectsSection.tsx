
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink, Code, Sparkles } from "lucide-react";
import { useState } from "react";

const ProjectsSection = () => {
  const [filter, setFilter] = useState("all");

  const orivoxProjects = [
    {
      name: "Script&Style",
      description: "Freelancing platform with immersive 3D interactions and seamless project management",
      stack: ["React", "Three.js", "Node.js", "MongoDB"],
      tags: ["3D", "Web Development", "Freelancing"],
      category: "platform"
    },
    {
      name: "TD - Transparent Display",
      description: "Cutting-edge transparent display technology for next-generation interfaces",
      stack: ["Python", "OpenCV", "Hardware Integration"],
      tags: ["Hardware", "AI", "Innovation"],
      category: "hardware"
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      description: "Immersive learning platform leveraging AI and VR for enhanced education",
      stack: ["React", "AI/ML", "WebXR", "PostgreSQL"],
      tags: ["AI", "Education", "VR"],
      category: "ai"
    },
    {
      name: "ExpoEXO",
      description: "Advanced expo and project showcase platform with interactive presentations",
      stack: ["Next.js", "Three.js", "WebGL", "Prisma"],
      tags: ["3D", "Showcase", "Interactive"],
      category: "platform"
    }
  ];

  const filterCategories = [
    { key: "all", label: "All Projects" },
    { key: "platform", label: "Platforms" },
    { key: "ai", label: "AI Projects" },
    { key: "hardware", label: "Hardware" }
  ];

  const filteredProjects = filter === "all" 
    ? orivoxProjects 
    : orivoxProjects.filter(project => project.category === filter);

  return (
    <section id="projects" className="py-20">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">My Startup: ORIVOX</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Building the future through innovative technology solutions. 
            Explore our flagship products and cutting-edge projects.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {filterCategories.map((category) => (
            <Button
              key={category.key}
              variant={filter === category.key ? "default" : "outline"}
              onClick={() => setFilter(category.key)}
              className="transition-all duration-300"
            >
              {category.label}
            </Button>
          ))}
        </motion.div>

        {/* Featured ORIVOX Section */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <Card className="bg-gradient-to-br from-primary/10 to-purple-500/10 border-primary/20">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Sparkles className="h-12 w-12 text-primary" />
              </div>
              <CardTitle className="text-3xl mb-4">ORIVOX Ecosystem</CardTitle>
              <CardDescription className="text-lg">
                A comprehensive suite of innovative products designed to transform 
                how we interact with technology and learn in the digital age.
              </CardDescription>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <CardTitle className="text-xl group-hover:text-primary transition-colors">
                      {project.name}
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button size="sm" variant="ghost" className="p-2">
                        <Code className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="ghost" className="p-2">
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <CardDescription className="text-base">
                    {project.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">Tech Stack:</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.stack.map((tech) => (
                          <Badge key={tech} variant="secondary">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Tags:</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.tags.map((tag) => (
                          <Badge key={tag} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
