
import { motion } from "framer-motion";
import Navigation from "@/components/portfolio/Navigation";
import ProjectsSection from "@/components/portfolio/ProjectsSection";
import { useState, useEffect } from "react";

const Projects = () => {
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <Navigation darkMode={darkMode} setDarkMode={setDarkMode} />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-br from-background via-background to-secondary/5 text-foreground pt-16"
      >
        <ProjectsSection />
      </motion.div>
    </div>
  );
};

export default Projects;
