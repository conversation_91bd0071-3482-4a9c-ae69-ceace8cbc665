import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { 
  Code, 
  Database, 
  Brain, 
  Cpu, 
  Globe, 
  Smartphone,
  Cloud,
  GitBranch,
  Zap,
  Layers
} from "lucide-react";

const TechStackSection = () => {
  const techCategories = [
    {
      title: "Frontend",
      icon: Globe,
      color: "neon-blue",
      technologies: [
        { name: "React", level: 95, icon: "⚛️" },
        { name: "Next.js", level: 90, icon: "▲" },
        { name: "TypeScript", level: 88, icon: "📘" },
        { name: "Tailwind CSS", level: 92, icon: "🎨" },
        { name: "Three.js", level: 85, icon: "🎮" },
      ]
    },
    {
      title: "Backend",
      icon: Database,
      color: "neon-green",
      technologies: [
        { name: "Node.js", level: 90, icon: "🟢" },
        { name: "Python", level: 95, icon: "🐍" },
        { name: "PostgreSQL", level: 85, icon: "🐘" },
        { name: "MongoDB", level: 80, icon: "🍃" },
        { name: "<PERSON><PERSON>", level: 75, icon: "🔴" },
      ]
    },
    {
      title: "AI/ML",
      icon: Brain,
      color: "neon-purple",
      technologies: [
        { name: "TensorFlow", level: 88, icon: "🧠" },
        { name: "OpenAI", level: 95, icon: "🤖" },
        { name: "LangChain", level: 90, icon: "🔗" },
        { name: "Hugging Face", level: 85, icon: "🤗" },
        { name: "PyTorch", level: 82, icon: "🔥" },
      ]
    },
    {
      title: "DevOps",
      icon: Cloud,
      color: "neon-cyan",
      technologies: [
        { name: "Docker", level: 85, icon: "🐳" },
        { name: "AWS", level: 80, icon: "☁️" },
        { name: "Vercel", level: 90, icon: "▲" },
        { name: "GitHub Actions", level: 85, icon: "⚡" },
        { name: "Nginx", level: 75, icon: "🌐" },
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <section className="py-20 relative">
      {/* Background Effects */}
      <div className="absolute inset-0 tech-grid opacity-10"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-orbitron font-bold mb-6 neon-cyan">
            Tech Stack I Use
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto font-rajdhani">
            Cutting-edge technologies powering the future of digital innovation
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {techCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              variants={itemVariants}
              whileHover={{ scale: 1.02, y: -5 }}
              className="group"
            >
              <Card className="h-full glass border-neon-blue/20 hover:border-neon-cyan/40 transition-all duration-300 relative overflow-hidden">
                {/* Holographic effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-neon-blue/5 via-transparent to-neon-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <CardHeader className="text-center relative z-10">
                  <div className={`mx-auto mb-4 p-3 rounded-full glass border border-${category.color}/30`}>
                    <category.icon className={`h-8 w-8 text-${category.color}`} />
                  </div>
                  <CardTitle className={`text-xl font-orbitron text-${category.color}`}>
                    {category.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="relative z-10">
                  <div className="space-y-4">
                    {category.technologies.map((tech, techIndex) => (
                      <motion.div
                        key={tech.name}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ 
                          delay: categoryIndex * 0.1 + techIndex * 0.05,
                          duration: 0.5 
                        }}
                        viewport={{ once: true }}
                        className="flex items-center justify-between p-2 rounded-lg glass hover:bg-white/5 transition-all duration-200"
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{tech.icon}</span>
                          <span className="font-rajdhani font-medium text-gray-300">
                            {tech.name}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-16 h-1 bg-gray-700 rounded-full overflow-hidden">
                            <motion.div
                              className={`h-full bg-gradient-to-r from-${category.color} to-${category.color}/60`}
                              initial={{ width: 0 }}
                              whileInView={{ width: `${tech.level}%` }}
                              transition={{ 
                                delay: categoryIndex * 0.1 + techIndex * 0.05 + 0.3,
                                duration: 1,
                                ease: "easeOut"
                              }}
                              viewport={{ once: true }}
                            />
                          </div>
                          <span className={`text-sm font-roboto-mono text-${category.color} font-semibold`}>
                            {tech.level}%
                          </span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Floating Tech Icons */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[Code, Cpu, Zap, Layers, GitBranch, Smartphone].map((Icon, i) => (
            <motion.div
              key={i}
              className="absolute"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                rotate: [0, 180, 360],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 8 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 5,
              }}
            >
              <Icon className="h-6 w-6 text-neon-blue/20" />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TechStackSection;
