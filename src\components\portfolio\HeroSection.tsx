
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Download, Zap, Code, Cpu } from "lucide-react";
import { useEffect, useState } from "react";
import { ReactTyped } from "react-typed";

const HeroSection = () => {
  const [currentText, setCurrentText] = useState(0);
  const texts = ["AI Engineer", "Prompt Master", "Startup Founder", "Full-Stack Developer"];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % texts.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20">
      {/* Tech Grid Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-tech-dark via-tech-darker to-tech-gray">
        <div className="absolute inset-0 tech-grid opacity-30"></div>
        <div className="absolute inset-0 bg-tech-grid opacity-20"></div>
        
        {/* Neon Floating Orbs */}
        <motion.div
          className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-neon-purple/20 to-neon-pink/20 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-neon-blue/20 to-neon-cyan/20 rounded-full blur-3xl"
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 w-96 h-96 bg-gradient-to-r from-neon-green/15 to-neon-cyan/15 rounded-full blur-3xl"
          animate={{
            x: [-50, 50, -50],
            y: [-30, 30, -30],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear",
          }}
        />

        {/* Circuit Lines */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`circuit-${i}`}
            className="absolute circuit-line"
            style={{
              width: `${Math.random() * 200 + 100}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              transform: `rotate(${Math.random() * 360}deg)`,
            }}
            animate={{
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.5,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 z-10">
        <div className="text-center max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-orbitron font-bold mb-6">
              <span className="text-white">Hi, I'm{" "}</span>
              <span className="neon-cyan animate-glow-pulse">
                BALA
              </span>
            </h1>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-2xl md:text-4xl mb-8 h-20 font-rajdhani"
          >
            <span className="text-gray-300">I'm a </span>
            <div className="inline-block">
              <ReactTyped
                strings={texts}
                typeSpeed={50}
                backSpeed={30}
                backDelay={2000}
                loop
                className="neon-blue font-semibold"
              />
            </div>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto font-rajdhani leading-relaxed"
          >
            <span className="text-neon-green">Building the future</span> with AI-powered solutions and innovative startups.
            Founder of <span className="neon-purple font-semibold">ORIVOX</span>, creating immersive digital experiences.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-6 justify-center"
          >
            <Button size="lg" className="tech-button group relative overflow-hidden px-8 py-4 font-orbitron font-semibold">
              <Zap className="mr-2 h-5 w-5" />
              Hire Me
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button variant="outline" size="lg" className="glass group relative overflow-hidden px-8 py-4 font-orbitron font-semibold border-neon-cyan/50 text-neon-cyan hover:text-white">
              <Code className="mr-2 h-5 w-5" />
              Explore Projects
              <Cpu className="ml-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-300" />
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Tech Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Neon floating particles */}
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={`particle-${i}`}
            className={`absolute w-1 h-1 rounded-full ${
              i % 3 === 0 ? 'bg-neon-blue' : i % 3 === 1 ? 'bg-neon-cyan' : 'bg-neon-green'
            } shadow-lg`}
            style={{
              boxShadow: `0 0 10px currentColor`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -50, 0],
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1.5, 0.5],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}

        {/* Tech geometric shapes */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`tech-shape-${i}`}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, 60, 0],
              y: [0, -40, 0],
              rotate: [0, 180, 360],
              opacity: [0.2, 0.8, 0.2],
            }}
            transition={{
              duration: 10 + Math.random() * 5,
              repeat: Infinity,
              delay: Math.random() * 4,
            }}
          >
            {i % 3 === 0 ? (
              <div className="w-6 h-6 border-2 border-neon-blue/40 rotate-45 animate-pulse" />
            ) : i % 3 === 1 ? (
              <div className="w-4 h-4 bg-neon-cyan/40 rounded-full animate-pulse" />
            ) : (
              <div className="w-5 h-1 bg-gradient-to-r from-neon-purple/40 to-neon-pink/40" />
            )}
          </motion.div>
        ))}

        {/* Connecting tech lines */}
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={`tech-line-${i}`}
            className="absolute bg-gradient-to-r from-transparent via-neon-blue/30 to-transparent"
            style={{
              width: `${Math.random() * 150 + 100}px`,
              height: '2px',
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              transform: `rotate(${Math.random() * 360}deg)`,
            }}
            animate={{
              opacity: [0, 1, 0],
              scaleX: [0, 1, 0],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSection;
