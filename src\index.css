@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&family=Roboto+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode - Tech inspired */
    --background: 240 10% 98%;
    --foreground: 240 10% 3.9%;

    --card: 240 10% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 240 10% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 200 100% 50%;
    --primary-foreground: 240 10% 3.9%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 200 100% 50%;

    --radius: 0.75rem;

    --sidebar-background: 240 10% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 200 100% 50%;
    --sidebar-primary-foreground: 240 10% 3.9%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 200 100% 50%;
  }

  .dark {
    /* Dark mode - Deep space tech theme */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 180 100% 50%;
    --primary-foreground: 240 10% 3.9%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 180 100% 50%;

    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 180 100% 50%;
    --sidebar-primary-foreground: 240 10% 3.9%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 180 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-rajdhani;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-orbitron;
  }

  code, pre {
    @apply font-roboto-mono;
  }
}

@layer components {
  /* Glassmorphism effect */
  .glass {
    @apply bg-white/10 dark:bg-white/5 backdrop-blur-lg border border-white/20 dark:border-white/10;
  }

  /* Neon glow effects */
  .neon-blue {
    @apply text-neon-blue;
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }

  .neon-cyan {
    @apply text-neon-cyan;
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }

  .neon-green {
    @apply text-neon-green;
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }

  .neon-purple {
    @apply text-neon-purple;
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }

  /* Tech button styles */
  .tech-button {
    @apply relative overflow-hidden bg-gradient-to-r from-neon-blue/20 to-neon-purple/20
           border border-neon-blue/50 text-neon-blue hover:text-white
           transition-all duration-300 hover:shadow-lg hover:shadow-neon-blue/25;
  }

  .tech-button::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-neon-blue to-neon-purple opacity-0
           transition-opacity duration-300;
  }

  .tech-button:hover::before {
    @apply opacity-20;
  }

  /* Circuit lines */
  .circuit-line {
    @apply absolute bg-gradient-to-r from-transparent via-neon-blue/50 to-transparent;
    height: 1px;
    animation: circuit-flow 3s linear infinite;
  }

  @keyframes circuit-flow {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
  }

  /* Tech grid background */
  .tech-grid {
    background-image:
      linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Holographic effect */
  .holographic {
    @apply bg-gradient-to-r from-neon-blue/10 via-neon-purple/10 to-neon-cyan/10
           border border-neon-blue/30 backdrop-blur-sm;
    background-size: 200% 200%;
    animation: holographic-shift 4s ease-in-out infinite;
  }

  @keyframes holographic-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
}