
import { motion, useInView } from "framer-motion";
import Navigation from "@/components/portfolio/Navigation";
import TechStackSection from "@/components/portfolio/TechStackSection";
import { useState, useRef, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

const About = () => {
  const [darkMode, setDarkMode] = useState(true); // Default to dark mode
  const counterRef = useRef(null);
  const skillsRef = useRef(null);
  const isCounterInView = useInView(counterRef, { once: true });
  const isSkillsInView = useInView(skillsRef, { once: true });

  useEffect(() => {
    // Set dark mode as default on initial load
    document.documentElement.classList.add('dark');

    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  // Animated Counter Hook
  const useAnimatedCounter = (end: number, duration: number = 2) => {
    const [count, setCount] = useState(0);

    useEffect(() => {
      if (!isCounterInView) return;

      let startTime: number;
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / (duration * 1000), 1);
        setCount(Math.floor(progress * end));

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    }, [end, duration, isCounterInView]);

    return count;
  };

  const projectsCount = useAnimatedCounter(50);
  const aiModelsCount = useAnimatedCounter(15);
  const experienceCount = useAnimatedCounter(5);

  const skills = [
    { name: "AI Agent Development", level: 95 },
    { name: "Prompt Engineering", level: 92 },
    { name: "React Development", level: 88 },
    { name: "Machine Learning", level: 85 },
    { name: "Leadership", level: 90 },
    { name: "Startup Strategy", level: 87 },
    { name: "3D Development", level: 82 },
    { name: "Web3 Technologies", level: 78 }
  ];

  const techStack = [
    "React", "Next.js", "TypeScript", "Python", "TensorFlow",
    "OpenAI", "Node.js", "Three.js", "Tailwind CSS", "PostgreSQL"
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <div className={`min-h-screen ${darkMode ? 'dark' : ''}`}>
      <Navigation darkMode={darkMode} setDarkMode={setDarkMode} />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="bg-gradient-to-br from-background via-background to-secondary/10 text-foreground pt-24 pb-12"
      >
        <div className="container mx-auto px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="max-w-6xl mx-auto"
          >
            {/* Hero Section */}
            <motion.div variants={itemVariants} className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl font-orbitron font-bold mb-6 neon-cyan animate-glow-pulse">
                About Me
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto font-rajdhani leading-relaxed">
                Passionate <span className="neon-green">AI engineer</span> and <span className="neon-purple">startup founder</span>,
                dedicated to creating innovative solutions
              </p>
            </motion.div>

            {/* Animated Counters */}
            <motion.div
              ref={counterRef}
              variants={itemVariants}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
            >
              {[
                { count: projectsCount, label: "Projects Completed", suffix: "+" },
                { count: aiModelsCount, label: "AI Models Fine-tuned", suffix: "+" },
                { count: experienceCount, label: "Years Experience", suffix: "+" }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Card className="text-center hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-card to-card/50 border-primary/20">
                    <CardContent className="pt-8 pb-6">
                      <motion.div
                        className="text-5xl font-bold text-primary mb-2"
                        initial={{ scale: 0 }}
                        animate={isCounterInView ? { scale: 1 } : { scale: 0 }}
                        transition={{ delay: index * 0.2, type: "spring", stiffness: 200 }}
                      >
                        {stat.count}{stat.suffix}
                      </motion.div>
                      <div className="text-muted-foreground font-medium">{stat.label}</div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>

            {/* Bio Section */}
            <motion.div variants={itemVariants} className="mb-16">
              <Card className="bg-gradient-to-r from-card/50 to-card border-primary/10">
                <CardHeader>
                  <CardTitle className="text-2xl">My Journey</CardTitle>
                </CardHeader>
                <CardContent className="prose prose-lg max-w-none dark:prose-invert">
                  <p className="text-lg leading-relaxed mb-4">
                    I'm a passionate AI engineer and startup founder with a deep commitment to 
                    creating innovative solutions that bridge the gap between cutting-edge technology 
                    and real-world applications. My journey in tech began with a fascination for 
                    how artificial intelligence could transform everyday experiences.
                  </p>
                  <p className="text-lg leading-relaxed">
                    As the founder of ORIVOX, I've dedicated myself to building a comprehensive 
                    ecosystem of products that leverage AI, 3D technologies, and immersive experiences 
                    to solve complex problems in education, freelancing, and digital interaction.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            {/* Skills with Progress Bars */}
            <motion.div
              ref={skillsRef}
              variants={itemVariants}
              className="mb-16"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl">Core Skills</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {skills.map((skill, index) => (
                      <motion.div
                        key={skill.name}
                        initial={{ opacity: 0, x: -50 }}
                        animate={isSkillsInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
                        transition={{ delay: index * 0.1, duration: 0.6 }}
                        className="space-y-2"
                      >
                        <div className="flex justify-between">
                          <span className="font-medium">{skill.name}</span>
                          <span className="text-sm text-muted-foreground">{skill.level}%</span>
                        </div>
                        <Progress 
                          value={isSkillsInView ? skill.level : 0} 
                          className="h-2"
                        />
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Tech Stack & Experience */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <motion.div variants={itemVariants}>
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle>Tech Stack</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-3">
                      {techStack.map((tech, index) => (
                        <motion.div
                          key={tech}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.05, duration: 0.3 }}
                          whileHover={{ scale: 1.1, y: -2 }}
                        >
                          <Badge variant="outline" className="hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer">
                            {tech}
                          </Badge>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={itemVariants}>
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle>Experience</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {[
                        {
                          title: "Founder & CEO, ORIVOX",
                          period: "2022 - Present",
                          description: "Leading innovation in AI and immersive technologies"
                        },
                        {
                          title: "Senior AI Engineer, TechCorp",
                          period: "2020 - 2022",
                          description: "Developed enterprise AI solutions"
                        }
                      ].map((exp, index) => (
                        <motion.div
                          key={exp.title}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.5 + index * 0.2 }}
                          className="border-l-2 border-primary/20 pl-4 pb-4"
                        >
                          <h3 className="font-semibold text-lg">{exp.title}</h3>
                          <p className="text-primary font-medium">{exp.period}</p>
                          <p className="text-muted-foreground">{exp.description}</p>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Tech Stack Section */}
            <TechStackSection />
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default About;
